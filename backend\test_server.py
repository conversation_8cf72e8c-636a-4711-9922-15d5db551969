#!/usr/bin/env python3
"""
Simple test server for E2E testing
Uses SQLite and minimal configuration
"""

import os
import sys
from flask import Flask
from flask_cors import CORS

# Set environment for testing
os.environ['FLASK_ENV'] = 'testing'
os.environ['DATABASE_URL'] = 'sqlite:///test_e2e.db'

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models import db, User
from routes_auth import register_auth_routes

def create_test_app():
    """Create a minimal Flask app for E2E testing"""
    app = Flask(__name__)
    
    # Basic configuration
    app.config['SECRET_KEY'] = 'test-secret-key'
    app.config['JWT_SECRET_KEY'] = 'test-jwt-secret-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_e2e.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['TESTING'] = False  # We want a real server, not test mode
    app.config['DEBUG'] = True
    
    # Enable CORS
    CORS(app, origins=['http://localhost:5173', 'http://localhost:3000'])
    
    # Initialize database
    db.init_app(app)
    
    # Register auth routes
    register_auth_routes(app)
    
    # Add basic health check
    @app.route('/health')
    def health():
        return {'status': 'ok', 'message': 'Test server running'}
    
    @app.route('/api/health')
    def api_health():
        return {'status': 'ok', 'message': 'API server running'}
    
    # Create tables and test user
    with app.app_context():
        db.create_all()
        
        # Create test admin user if it doesn't exist
        admin = User.query.filter_by(employee_number='ADMIN001').first()
        if not admin:
            admin = User(
                name='Test Admin',
                employee_number='ADMIN001',
                department='IT',
                is_admin=True,
                is_active=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Created test admin user: ADMIN001 / admin123")
        
        # Create test regular user if it doesn't exist
        user = User.query.filter_by(employee_number='USER001').first()
        if not user:
            user = User(
                name='Test User',
                employee_number='USER001',
                department='Engineering',
                is_admin=False,
                is_active=True
            )
            user.set_password('user123')
            db.session.add(user)
            db.session.commit()
            print("Created test user: USER001 / user123")
    
    return app

if __name__ == '__main__':
    app = create_test_app()
    print("Starting test server on http://localhost:5000")
    print("Test users:")
    print("  Admin: ADMIN001 / admin123")
    print("  User:  USER001 / user123")
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
